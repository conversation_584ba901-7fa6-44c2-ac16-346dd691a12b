#include "asyncdataprocessor.h"
#include "log.h"
#include <QDateTime>
#include <QtConcurrent>

AsyncDataProcessor::AsyncDataProcessor(QObject *parent)
    : QObject(parent)
    , m_schedulingTimer(nullptr)
{
    logInfo("AsyncDataProcessor created");
}

AsyncDataProcessor::~AsyncDataProcessor()
{
    stop();
    logInfo("AsyncDataProcessor destroyed");
}

bool AsyncDataProcessor::start(const Config& config)
{
    if (m_running.load()) {
        logWarnning("AsyncDataProcessor is already running");
        return false;
    }

    logInfo("Starting AsyncDataProcessor...");

    try {
        // 保存配置
        m_config = config;

        // 初始化组件
        if (!initializeComponents(config)) {
            logError("Failed to initialize components");
            return false;
        }

        // 设置线程池
        m_threadPool.setMaxThreadCount(static_cast<int>(config.processingThreads));
        logInfo(QString("Thread pool configured with %1 threads").arg(config.processingThreads));

        // 创建并启动调度定时器
        m_schedulingTimer = new QTimer(this);
        m_schedulingTimer->setInterval(static_cast<int>(config.schedulingInterval));
        connect(m_schedulingTimer, &QTimer::timeout, this, &AsyncDataProcessor::onSchedulingTimer);
        m_schedulingTimer->start();

        // 设置运行状态
        m_running.store(true);
        updateGlobalLastUpdateTime();

        logInfo(QString("AsyncDataProcessor started successfully with config: "
                       "maxCacheSize=%1, batchSize=%2, maxBatchSize=%3, timeThreshold=%4ms, "
                       "idleThreshold=%5ms, processingThreads=%6, schedulingInterval=%7ms")
               .arg(config.maxTotalCacheSize).arg(config.batchSize).arg(config.maxBatchSize)
               .arg(config.timeThreshold).arg(config.idleThreshold)
               .arg(config.processingThreads).arg(config.schedulingInterval));

        emit statusChanged(true);
        return true;

    } catch (const std::exception& e) {
        logError(QString("Exception during AsyncDataProcessor start: %1").arg(e.what()));
        cleanup();
        return false;
    } catch (...) {
        logError("Unknown exception during AsyncDataProcessor start");
        cleanup();
        return false;
    }
}

void AsyncDataProcessor::stop()
{
    if (!m_running.load()) {
        logTrace("AsyncDataProcessor is not running");
        return;
    }

    logInfo("Stopping AsyncDataProcessor...");

    try {
        // 设置停止状态
        m_running.store(false);

        // 停止调度定时器
        if (m_schedulingTimer) {
            m_schedulingTimer->stop();
            m_schedulingTimer->deleteLater();
            m_schedulingTimer = nullptr;
        }

        // 等待线程池完成所有任务
        logInfo("Waiting for thread pool to finish...");
        m_threadPool.waitForDone(10000); // 最多等待10秒

        // 清理资源
        cleanup();

        logInfo(QString("AsyncDataProcessor stopped - Total data added: %1, processed: %2, scheduling cycles: %3")
               .arg(m_totalDataAdded.load()).arg(m_totalDataProcessed.load()).arg(m_totalSchedulingCycles.load()));

        emit statusChanged(false);

    } catch (const std::exception& e) {
        logError(QString("Exception during AsyncDataProcessor stop: %1").arg(e.what()));
    } catch (...) {
        logError("Unknown exception during AsyncDataProcessor stop");
    }
}

bool AsyncDataProcessor::addData(const QString& sensorId, const QList<QSharedPointer<DataItem>>& items)
{
    if (!m_running.load()) {
        logWarnning("AsyncDataProcessor is not running, cannot add data");
        return false;
    }

    if (sensorId.isEmpty()) {
        logError("Empty sensor ID provided to addData");
        return false;
    }

    if (items.isEmpty()) {
        logTrace(QString("Empty data list provided for sensor: %1").arg(sensorId));
        return true; // 空数据不算错误
    }

    try {
        // 检查背压
        size_t currentSize = m_cacheManager->getTotalSize();
        size_t maxSize = m_cacheManager->getMaxTotalSize();
        
        auto backpressureLevel = m_backpressureController->checkAndTriggerBackpressure(sensorId, currentSize, maxSize);
        
        if (backpressureLevel == BackpressureController::BackpressureLevel::Block) {
            logWarnning(QString("Data rejected due to backpressure for sensor: %1").arg(sensorId));
            emit backpressureTriggered(sensorId, true);
            return false;
        }

        // 添加数据到缓存
        bool success = m_cacheManager->addData(sensorId, items);
        if (success) {
            m_totalDataAdded += items.size();
            updateGlobalLastUpdateTime();
            
            logTrace(QString("Successfully added %1 items for sensor: %2").arg(items.size()).arg(sensorId));
        } else {
            logWarnning(QString("Failed to add data for sensor: %1").arg(sensorId));
        }

        return success;

    } catch (const std::exception& e) {
        logError(QString("Exception in addData for sensor %1: %2").arg(sensorId).arg(e.what()));
        return false;
    } catch (...) {
        logError(QString("Unknown exception in addData for sensor: %1").arg(sensorId));
        return false;
    }
}

void AsyncDataProcessor::setBackpressureCallback(std::function<void(const QString&, bool)> callback)
{
    if (m_backpressureController) {
        m_backpressureController->setBackpressureCallback(callback);
        logInfo("Backpressure callback set");
    } else {
        logError("Backpressure controller not initialized");
    }
}

QString AsyncDataProcessor::getPerformanceStatistics() const
{
    if (!m_running.load()) {
        return "AsyncDataProcessor is not running";
    }

    try {
        QString cacheStats = QString("Cache: total size %1/%2")
                            .arg(m_cacheManager->getTotalSize())
                            .arg(m_cacheManager->getMaxTotalSize());

        QString schedulerStats = m_scheduler->getSchedulingStatistics();
        QString processorStats = m_processor->getProcessingStatistics();
        QString backpressureStats = m_backpressureController->getBackpressureStatistics();

        QString globalStats = QString("Global: added %1, processed %2, cycles %3")
                             .arg(m_totalDataAdded.load())
                             .arg(m_totalDataProcessed.load())
                             .arg(m_totalSchedulingCycles.load());

        return QString("AsyncDataProcessor Performance Statistics:\n%1\n%2\n%3\n%4\n%5")
               .arg(globalStats).arg(cacheStats).arg(schedulerStats)
               .arg(processorStats).arg(backpressureStats);

    } catch (const std::exception& e) {
        return QString("Exception getting performance statistics: %1").arg(e.what());
    } catch (...) {
        return "Unknown exception getting performance statistics";
    }
}

QHash<QString, size_t> AsyncDataProcessor::getCacheStatistics() const
{
    if (m_cacheManager) {
        return m_cacheManager->getCacheStatistics();
    }
    return QHash<QString, size_t>();
}

void AsyncDataProcessor::resetAllStatistics()
{
    try {
        if (m_scheduler) {
            m_scheduler->resetSchedulingState();
        }
        if (m_processor) {
            m_processor->resetStatistics();
        }
        if (m_backpressureController) {
            m_backpressureController->resetStatistics();
        }

        m_totalDataAdded.store(0);
        m_totalDataProcessed.store(0);
        m_totalSchedulingCycles.store(0);

        logInfo("All statistics reset");

    } catch (const std::exception& e) {
        logError(QString("Exception resetting statistics: %1").arg(e.what()));
    } catch (...) {
        logError("Unknown exception resetting statistics");
    }
}

size_t AsyncDataProcessor::clearSensorCache(const QString& sensorId)
{
    if (m_cacheManager) {
        return m_cacheManager->clearSensorCache(sensorId);
    }
    return 0;
}

size_t AsyncDataProcessor::clearAllCaches()
{
    if (m_cacheManager) {
        return m_cacheManager->clearAllCaches();
    }
    return 0;
}

void AsyncDataProcessor::onSchedulingTimer()
{
    if (!m_running.load()) {
        return;
    }

    try {
        m_totalSchedulingCycles++;

        // 获取下一个需要处理的传感器
        QString sensorId = m_scheduler->getNextSensorToProcess(m_cacheManager.get());

        if (!sensorId.isEmpty()) {
            logTrace(QString("Scheduling processing for sensor: %1").arg(sensorId));
            submitProcessingTask(sensorId);
        }

    } catch (const std::exception& e) {
        logError(QString("Exception in scheduling timer: %1").arg(e.what()));
    } catch (...) {
        logError("Unknown exception in scheduling timer");
    }
}

void AsyncDataProcessor::onDataProcessed(const QString& sensorId, int processedCount, bool success)
{
    try {
        if (success) {
            m_totalDataProcessed += processedCount;
            logTrace(QString("Data processing completed for sensor %1: %2 items").arg(sensorId).arg(processedCount));
            emit dataProcessed(sensorId, processedCount);
        } else {
            logWarnning(QString("Data processing failed for sensor: %1").arg(sensorId));
        }

    } catch (const std::exception& e) {
        logError(QString("Exception in onDataProcessed: %1").arg(e.what()));
    } catch (...) {
        logError("Unknown exception in onDataProcessed");
    }
}

void AsyncDataProcessor::onProcessingError(const QString& sensorId, const QString& errorMessage)
{
    try {
        logError(QString("Processing error for sensor %1: %2").arg(sensorId).arg(errorMessage));
        emit processingError(sensorId, errorMessage);

    } catch (const std::exception& e) {
        logError(QString("Exception in onProcessingError: %1").arg(e.what()));
    } catch (...) {
        logError("Unknown exception in onProcessingError");
    }
}

void AsyncDataProcessor::submitProcessingTask(const QString& sensorId)
{
    if (!m_running.load()) {
        return;
    }

    try {
        auto task = [this, sensorId]() {
            try {
                // 计算批处理大小
                qint64 timeSinceLastUpdate = getTimeSinceLastGlobalUpdate();
                size_t batchSize = m_scheduler->calculateBatchSize(timeSinceLastUpdate);

                // 从缓存提取数据
                SensorDataCache* cache = m_cacheManager->getSensorCache(sensorId);
                if (!cache) {
                    logWarnning(QString("Cache not found for sensor: %1").arg(sensorId));
                    m_scheduler->markProcessingComplete(sensorId);
                    return;
                }

                QList<QSharedPointer<DataItem>> dataItems = cache->extractData(batchSize);
                if (dataItems.isEmpty()) {
                    logTrace(QString("No data to process for sensor: %1").arg(sensorId));
                    m_scheduler->markProcessingComplete(sensorId);
                    return;
                }

                logTrace(QString("Processing %1 items for sensor %2 with batch size %3")
                        .arg(dataItems.size()).arg(sensorId).arg(batchSize));

                // 处理数据
                bool success = m_processor->processData(sensorId, dataItems);

                // 标记处理完成
                m_scheduler->markProcessingComplete(sensorId);

                logTrace(QString("Processing task completed for sensor %1, success: %2")
                        .arg(sensorId).arg(success ? "true" : "false"));

            } catch (const std::exception& e) {
                logError(QString("Exception in processing task for sensor %1: %2").arg(sensorId).arg(e.what()));
                m_scheduler->markProcessingComplete(sensorId);
            } catch (...) {
                logError(QString("Unknown exception in processing task for sensor: %1").arg(sensorId));
                m_scheduler->markProcessingComplete(sensorId);
            }
        };

        QtConcurrent::run(&m_threadPool, task);

    } catch (const std::exception& e) {
        logError(QString("Exception submitting processing task for sensor %1: %2").arg(sensorId).arg(e.what()));
    } catch (...) {
        logError(QString("Unknown exception submitting processing task for sensor: %1").arg(sensorId));
    }
}

qint64 AsyncDataProcessor::getTimeSinceLastGlobalUpdate() const
{
    qint64 lastTime = m_lastGlobalUpdateTime.load();
    if (lastTime == 0) {
        return 0;
    }

    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    return currentTime - lastTime;
}

void AsyncDataProcessor::updateGlobalLastUpdateTime()
{
    m_lastGlobalUpdateTime.store(QDateTime::currentMSecsSinceEpoch());
}

bool AsyncDataProcessor::initializeComponents(const Config& config)
{
    try {
        // 创建缓存管理器
        m_cacheManager = std::make_unique<CacheManager>(this);
        m_cacheManager->setMaxTotalSize(config.maxTotalCacheSize);

        // 创建处理调度器
        m_scheduler = std::make_unique<ProcessScheduler>();
        m_scheduler->setBatchSize(config.batchSize);
        m_scheduler->setMaxBatchSize(config.maxBatchSize);
        m_scheduler->setTimeThreshold(config.timeThreshold);
        m_scheduler->setIdleThreshold(config.idleThreshold);

        // 创建数据处理器
        m_processor = std::make_unique<DataProcessor>(this);

        // 连接数据处理器信号
        connect(m_processor.get(), &DataProcessor::dataProcessed,
                this, &AsyncDataProcessor::onDataProcessed);
        connect(m_processor.get(), &DataProcessor::processingError,
                this, &AsyncDataProcessor::onProcessingError);

        // 创建背压控制器
        m_backpressureController = std::make_unique<BackpressureController>(this);
        m_backpressureController->setWarningThreshold(config.warningThreshold);
        m_backpressureController->setThrottleThreshold(config.throttleThreshold);
        m_backpressureController->setBlockThreshold(config.blockThreshold);

        // 连接背压控制器信号
        connect(m_backpressureController.get(), &BackpressureController::backpressureLevelChanged,
                this, [this](const QString& sensorId, int level, double utilizationRate) {
                    bool isBlocked = (level == static_cast<int>(BackpressureController::BackpressureLevel::Block));
                    emit backpressureTriggered(sensorId, isBlocked);
                });

        logInfo("All components initialized successfully");
        return true;

    } catch (const std::exception& e) {
        logError(QString("Exception initializing components: %1").arg(e.what()));
        return false;
    } catch (...) {
        logError("Unknown exception initializing components");
        return false;
    }
}

void AsyncDataProcessor::cleanup()
{
    try {
        // 重置组件（智能指针会自动清理）
        m_backpressureController.reset();
        m_processor.reset();
        m_scheduler.reset();
        m_cacheManager.reset();

        logTrace("Components cleanup completed");

    } catch (const std::exception& e) {
        logError(QString("Exception during cleanup: %1").arg(e.what()));
    } catch (...) {
        logError("Unknown exception during cleanup");
    }
}
