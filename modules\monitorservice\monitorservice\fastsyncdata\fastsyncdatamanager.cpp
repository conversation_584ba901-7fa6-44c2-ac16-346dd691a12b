#include "fastsyncdatamanager.h"
#include "wificommunication/wificommunicationmanager.h"
#include "businesstask.h"
#include "log.h"
#include "uploaddatahandler.h"
//#include "uploaddatabackgroundprocessor.h"
//#include "streamdataprocessor.h"
#include "asyncdataprocessor/asyncdataprocessor.h"

FastSyncDataManager::FastSyncDataManager(QObject *parent) :
    QObject(parent),
    m_spWifiCommManager(new WifiCommunicationManager(this)),
    m_pThreadPool(nullptr),
    m_spWorkerThread(new QThread)
{
    // 连接信号和槽
    connect(m_spWifiCommManager.data(), &WifiCommunicationManager::businessDataReceived, this, &FastSyncDataManager::handleDataReceived);
    connect(m_spWifiCommManager.data(), &WifiCommunicationManager::sessionDisconnected, this, &FastSyncDataManager::handleSessionDisconnected);

    connect(this, &FastSyncDataManager::sigFastSyncDataStart, this, &FastSyncDataManager::onFastSyncDataStart);
    connect(this, &FastSyncDataManager::sigFastSyncDataCancel, this, &FastSyncDataManager::onFastSyncDataCancel);

    qRegisterMetaType<FastSyncData::SyncDataTaskInfo>("FastSyncData::SyncDataTaskInfo");
    
    this->moveToThread(m_spWorkerThread.data());
    m_spWorkerThread->start();
}

FastSyncDataManager::~FastSyncDataManager()
{
    if (thread() != QThread::currentThread()) {
        disconnect();
        QMetaObject::invokeMethod(this, "stopFastSyncData", Qt::BlockingQueuedConnection);
    } else {
        stopFastSyncData();
    }

    if(m_spWorkerThread) {
        m_spWorkerThread->quit();
        m_spWorkerThread->wait();
    }
}

bool FastSyncDataManager::startFastSyncData(const FastSyncData::SyncDataTaskInfo &syncDataTaskInfo)
{
    emit sigFastSyncDataStart(syncDataTaskInfo);
    return true;
}

bool FastSyncDataManager::cancelFastSyncData()
{
    emit sigFastSyncDataCancel();
    return true;
}

FastSyncData::SyncDataTaskInfo FastSyncDataManager::getFastSyncDataTaskInfo()
{
    if(m_currentTask.isNull()) {
        logWarnning("current task is null");
        return FastSyncData::SyncDataTaskInfo();
    }

    return m_currentTask->getSyncDataTaskInfo();
}

bool FastSyncDataManager::stopFastSyncData()
{
    logInfo("FastSyncDataManager::stopFastSyncData");

    // 停止数据上传后台处理器
    if (m_uploadProcessor) {
        logInfo("Stopping upload data background processor...");
        m_uploadProcessor->stop();
        m_uploadProcessor.clear();
        logInfo("Upload data background processor stopped");
    }

    if(m_spSensorWakeupWorker && m_spSensorWakeupWorker->isWakeupRunning())
    {
        logInfo("Stop sensor wakeup worker");
        m_spSensorWakeupWorker->stopWakeup();
        m_spSensorWakeupWorker.reset();
    }

    logInfo("Stop wifi communication manager");
    // 停止通信服务
    m_spWifiCommManager->stopServer();

    logInfo("Clear session sensor map");
    //清理会话
    {
        QWriteLocker locker(&m_sessionSensorMapLock);
        m_sessionSensorMap.clear();
    }

    logInfo("Release thread pool");
    // 释放线程池
    releaseThreadPool();

    logInfo("Kill sync task status check timer");
    // 停止定时器
    killTimer(m_syncTaskStatusCheckTimerId);
    m_syncTaskStatusCheckTimerId = 0;

    logInfo("Set task status to stopped");
    // 设置任务状态为停止
    m_currentTask->setRunning(false);
    
    return true;
}

SyncTaskStatus FastSyncDataManager::getFastSyncDataStatus(int pageIndex, int pageSize)
{
    if(pageIndex <= 0 || pageSize <= 0) {
        logWarnning("getFastSyncDataStatus error: pageIndex or pageSize is invalid") << pageIndex << pageSize;
        return SyncTaskStatus();
    }
    
    if(m_currentTask.isNull()) {
        logWarnning("current task is null");
        return SyncTaskStatus();
    }

    SyncTaskStatus status;
    status.setRunning(m_currentTask->isRunning());
    status.setLastErrorCode(m_currentTask->getLastErrorCode());
    status.setSyncDataTaskInfo(m_currentTask->getSyncDataTaskInfo());
    status.setSyncPointStatusList(m_currentTask->getSyncPointStatusList(pageIndex, pageSize));
    return status;
}

void FastSyncDataManager::handleDataReceived(const QString &sessionId, const QByteArray &data)
{
    logTrace("FastSyncDataManager::handleDataReceived, sessionId:" << sessionId << ", data size:" << data.size()
            << ", object thread:" << thread() << ", current thread:" << QThread::currentThread());
    
    //检查当前任务是否正在运行
    if(!hasRunningTask()) {
        logWarnning("FastSyncDataManager::handleDataReceived:No running task");
        return;
    }

    if(data.isEmpty()) {
        logWarnning("FastSyncDataManager::handleDataReceived:Received data is empty, sessionId:") << sessionId;
        return;
    }

    // 获取业务类型
    const int businessType = m_protocolProcessor.getBusinessType(data);

    // 创建对应的任务并提交到线程池
    QRunnable* task = nullptr;
    
    switch (businessType) {
        case BusinessProtocol::BusinessType::SENSOR_CONNECTION:
            task = new SensorConnectionTask(sessionId, data, this);
            break;

        case BusinessProtocol::BusinessType::DATA_REQUEST:
            task = new SensorDataRequestResponseTask(sessionId, data, this);
            break;
            
        case BusinessProtocol::BusinessType::DATA_UPLOAD:
            task = new DataUploadTask(sessionId, data, this);
            break;
            
        default:
            logWarnning(QString("FastSyncDataManager::handleDataReceived:Unknown business type: %1").arg(businessType));
            return;
    }
    
    // 设置自动删除，线程池会在任务完成后自动删除任务对象
    task->setAutoDelete(true);
    
    // 提交任务到线程池
    m_pThreadPool->start(task);
}

void FastSyncDataManager::handleSessionDisconnected(const QString &sessionId)
{
    QWriteLocker locker(&m_sessionSensorMapLock);

    // 检查会话ID是否在任务中
    if(!m_sessionSensorMap.contains(sessionId)) {
        logWarnning(QString("FastSyncDataManager::handleSessionDisconnected:Session %1 not mapped to sensor").arg(sessionId));
        return;
    }

    const QString& sensorId = m_sessionSensorMap[sessionId];
    logInfo(QString("FastSyncDataManager::handleSessionDisconnected:Sensor %1 disconnected, sessionId: %2").arg(sensorId, sessionId));

    //更新传感器对应测点状态为断开连接
    m_currentTask->updateSyncPointState(sensorId, FastSyncData::SyncPointState::Failed, FastSyncData::SyncPointErrorCode::Disconnected);

    // 从会话ID到传感器ID的映射中移除该会话ID
    m_sessionSensorMap.remove(sessionId);

    emit sigFastSyncDataStatusUpdate();
}

void FastSyncDataManager::checkSyncTaskStatus()
{
    logTrace("FastSyncDataManager::checkSyncTaskStatus");

    if(!hasRunningTask()) {
        logWarnning("FastSyncDataManager::checkSyncTaskStatus:No running task");
        return;
    }

    // 检查同步任务是否超时 80s
    if(m_currentTask->checkSyncTimeout(80))
    {
        emit sigFastSyncDataStatusUpdate();
    }

    //检查同步任务是否完成
    if(m_currentTask->isAllSynced()) {
        logInfo("FastSyncDataManager::checkSyncTaskStatus:All sensors synced");

        stopFastSyncData();

        emit sigFastSyncDataStatusUpdate();
    }
}

void FastSyncDataManager::handleSensorConnection(const QString &sessionId, const QSharedPointer<SensorConnectionMessage> &message)
{
    const QString sensorId = message->getSensorId();

    logInfo(QString("handleSensorConnection:Sensor [%1] connect, sessionId: %2").arg(sensorId, sessionId));

    //检查传感器ID是否在任务中
    if(!m_currentTask->isSensorInTask(sensorId)) {
        logWarnning(QString("Sensor [%1] not in task, reject connection").arg(sensorId));

        //拒绝连接
        QSharedPointer<ConnectionResponseMessage> responseMessage = BusinessMessageFactory::createConnectionResponseMessage(false);
        m_spWifiCommManager->sendData(sessionId, responseMessage->serialize());
        return;
    }

    //接受连接
    QSharedPointer<ConnectionResponseMessage> responseMessage = BusinessMessageFactory::createConnectionResponseMessage(true);
    m_spWifiCommManager->sendData(sessionId, responseMessage->serialize());
    
    //映射会话ID到传感器ID
    {
        QWriteLocker locker(&m_sessionSensorMapLock);
        m_sessionSensorMap.insert(sessionId, sensorId);
        logInfo(QString("Session [%1] mapped to sensor [%2]").arg(sessionId, sensorId));
    }
    
    // 更新传感器对应测点状态为已连接
    m_currentTask->updateSyncPointState(sensorId, FastSyncData::SyncPointState::Connected); 
    
    emit sigFastSyncDataStatusUpdate();
    logInfo(QString("Sensor [%1], connection accepted.").arg(sensorId));

    // 获取传感器同步信息并打印
    SyncTask::SensorSyncInfo sensorSyncInfo = m_currentTask->getSensorSyncInfo(sensorId);
    
    // 构建时间范围详细信息
    QStringList timeRangeDetails;
    for (int i = 0; i < sensorSyncInfo.syncTimeRanges.size(); ++i) {
        const auto& timeRange = sensorSyncInfo.syncTimeRanges[i];
        timeRangeDetails << QString("Range[%1]={%2 to %3}")
                            .arg(i)
                            .arg(timeRange.startTime.toString("yyyy-MM-dd hh:mm:ss"))
                            .arg(timeRange.endTime.toString("yyyy-MM-dd hh:mm:ss"));
    }
    
    logInfo(QString("Sensor [%1] sync info: TimeRanges count=%2 [%3], ExistingDataIds count=%4")
            .arg(sensorId)
            .arg(sensorSyncInfo.syncTimeRanges.size())
            .arg(timeRangeDetails.join(", "))
            .arg(sensorSyncInfo.existingDataIds.size()));

    //向传感器发送获取数据请求
    QSharedPointer<DataRequestMessage> requestMessage =
            BusinessMessageFactory::createDataRequestMessage(sensorSyncInfo.syncTimeRanges);
    m_spWifiCommManager->sendData(sessionId, requestMessage->serialize());
}

void FastSyncDataManager::handleDataRequestResponse(const QString &sessionId, const QSharedPointer<DataRequestResponseMessage> &message)
{
    logTrace(QString("handleDataRequestResponse, sessionId: %1").arg(sessionId));
    
    QString sensorId;

    {
        QReadLocker locker(&m_sessionSensorMapLock);
        //检查会话ID是否在任务中
        if(!m_sessionSensorMap.contains(sessionId)) {
            logWarnning(QString("handleDataRequestResponse: Session [%1] not mapped to sensor").arg(sessionId));
            return;
        }

        sensorId = m_sessionSensorMap[sessionId];
        logTrace(QString("handleDataRequestResponse: Session [%1] mapped to sensor [%2]").arg(sessionId, sensorId));
    }

    //获取待同步数据总量
    const int dataCount = message->getDataCount();
    logInfo(QString("handleDataRequestResponse: Sensor [%1] reports %2 data items to sync").arg(sensorId, QString::number(dataCount)));

    //更新传感器待同步数据总量
    m_currentTask->updateSensorTotalItemCount(sensorId, dataCount);
    logTrace(QString("handleDataRequestResponse: Updated sensor [%1] total item count to %2").arg(sensorId, QString::number(dataCount)));

    if(0 == dataCount) {
        // 更新传感器状态为同步完成
        m_currentTask->updateSyncPointState(sensorId, FastSyncData::SyncPointState::Success);
        logInfo(QString("handleDataRequestResponse: Sensor [%1] state updated to Success").arg(sensorId));

        emit sigFastSyncDataStatusUpdate();
        logInfo(QString("handleDataRequestResponse: Sensor [%1] data request response processed, data count: %2").arg(sensorId, QString::number(dataCount)));
        return;
    }

    // 更新传感器状态为数据同步中
    m_currentTask->updateSyncPointState(sensorId, FastSyncData::SyncPointState::Syncing);
    logInfo(QString("handleDataRequestResponse: Sensor [%1] state updated to Syncing").arg(sensorId));

    emit sigFastSyncDataStatusUpdate();
    logInfo(QString("handleDataRequestResponse: Sensor [%1] data request response processed, data count: %2").arg(sensorId, QString::number(dataCount)));
}

void FastSyncDataManager::handleDataUpload(const QString &sessionId, const QSharedPointer<DataUploadMessage> &message)
{
    logTrace(QString("FastSyncDataManager::handleDataUpload, sessionId: %1").arg(sessionId));
    
    QString sensorId;

    {
        QReadLocker locker(&m_sessionSensorMapLock);
        //检查会话ID是否在任务中
        if(!m_sessionSensorMap.contains(sessionId)) {
            logWarnning(QString("handleDataUpload: Session [%1] not mapped to sensor").arg(sessionId));
            return;
        }

        sensorId = m_sessionSensorMap[sessionId];
        logTrace(QString("handleDataUpload: Session [%1] mapped to sensor [%2]").arg(sessionId, sensorId));
    }

    //收到数据即发送数据上传成功响应
    QSharedPointer<DataUploadResponseMessage> responseMessage = BusinessMessageFactory::createDataUploadResponseMessage(true);
    m_spWifiCommManager->sendData(sessionId, responseMessage->serialize());
    logTrace(QString("handleDataUpload: Sent upload response to sensor [%1]").arg(sensorId));

    // 获取数据项列表
    const QList<QSharedPointer<DataItem> > &dataItems = message->getDataItems();
    logInfo(QString("handleDataUpload: Sensor [%1] uploaded %2 data items").arg(sensorId).arg(dataItems.size()));

    // 将数据项添加到后台处理器缓存
    if (m_uploadProcessor) {
        m_uploadProcessor->addData(sensorId, dataItems);
        logTrace(QString("handleDataUpload: Added %1 data items to background processor cache for sensor [%2]")
                 .arg(dataItems.size()).arg(sensorId));
    } else {
        logError("handleDataUpload: Upload processor is not initialized");
    }

    logInfo(QString("handleDataUpload: Sensor [%1] data upload processing completed successfully").arg(sensorId));
}

void FastSyncDataManager::timerEvent(QTimerEvent *event)
{
    logTrace("FastSyncDataManager::timerEvent, event->timerId():" << event->timerId() 
    << ", object thread:" << thread() << ", current thread:" << QThread::currentThread());

    if(event->timerId() == m_syncTaskStatusCheckTimerId) {
        //检查同步任务状态
        checkSyncTaskStatus();
    }
}

bool FastSyncDataManager::hasRunningTask() const
{
    return (!m_currentTask.isNull() && m_currentTask->isRunning());
}

void FastSyncDataManager::initThreadPool(int maxThreadCount)
{
    releaseThreadPool();

    m_pThreadPool = new QThreadPool;
    m_pThreadPool->setMaxThreadCount(maxThreadCount);
}

void FastSyncDataManager::releaseThreadPool()
{
    if(m_pThreadPool) {
        //等待所有任务完成
        m_pThreadPool->clear();
        m_pThreadPool->waitForDone();
        delete m_pThreadPool;
        m_pThreadPool = nullptr;
    }
}

void FastSyncDataManager::onFastSyncDataStart(const FastSyncData::SyncDataTaskInfo& syncDataTaskInfo)
{
    // 构建任务信息字符串
    QString taskInfo = QString("FastSyncDataManager::onFastSyncDataStart - Task Info: StartTime=%1, EndTime=%2, PointCount=%3")
                      .arg(syncDataTaskInfo.dtStartTime.toString("yyyy-MM-dd hh:mm:ss"))
                      .arg(syncDataTaskInfo.dtEndTime.toString("yyyy-MM-dd hh:mm:ss"))
                      .arg(syncDataTaskInfo.lstPointList.size());
    
    // 添加测点详细信息
    QStringList pointDetails;
    for (int i = 0; i < syncDataTaskInfo.lstPointList.size(); ++i) {
        const auto& point = syncDataTaskInfo.lstPointList[i];
        pointDetails << QString("Point[%1]={GUID:%2,SensorID:%3}")
                        .arg(i).arg(point.strPointGUID).arg(point.strSensorID);
    }
    
    if (!pointDetails.isEmpty()) {
        taskInfo += QString(", Points=[%1]").arg(pointDetails.join(", "));
    }
    
    logInfo(taskInfo);
    
    // 检查是否已有正在运行的任务
    if (hasRunningTask()) {
        logWarnning("There is already a running task");
        return;
    }

    // 如果有之前的任务（非运行状态），先清除
    if (!m_currentTask.isNull()) {
        logWarnning("Clearing previous task");
        m_currentTask.clear();
    }

    // 创建新任务
    logInfo("Creating new sync task...");
    m_currentTask = QSharedPointer<SyncTask>(new SyncTask(syncDataTaskInfo));
    // 设置任务状态为运行中
    m_currentTask->setRunning(true);
    emit sigFastSyncDataStatusUpdate();
    logInfo("New sync task created and set to running state");

    // 创建并启动数据上传后台处理器
    logInfo("Creating and starting upload data background processor...");
    m_uploadProcessor = QSharedPointer<AsyncDataProcessor>::create();

    connect(m_uploadProcessor.data(), &AsyncDataProcessor::dataProcessed,
                    this, &FastSyncDataManager::sigFastSyncDataStatusUpdate);
    
    AsyncDataProcessor::Config processConfig;
    m_uploadProcessor->start(processConfig, m_currentTask);
    logInfo("Upload data background processor started successfully");
    

    // 启动通信服务
    logInfo("Starting WiFi communication server...");
    if(WifiCommunicationManager::NoError != m_spWifiCommManager->startServer(20000)) {
        logError("FastSyncDataManager::onFastSyncDataStart, startServer failed");
        m_currentTask->setLastErrorCode(FastSyncData::SyncTaskErrorCode::StartCommServiceFailed);
        m_currentTask->setTaskFailed();
        emit sigFastSyncDataStatusUpdate();
        return;
    }
    logInfo("WiFi communication server started successfully on port 20000");

    // 初始化线程池
    logInfo("Initializing thread pool with 2 threads...");
    initThreadPool(2);
    logInfo("Thread pool initialized successfully");

    // 初始化传感器同步信息
    logInfo("Initializing sensor sync info...");
    m_currentTask->initSensorSyncInfo();
    logInfo("Sensor sync info initialized successfully");

    // 创建传感器唤醒工作对象
    m_spSensorWakeupWorker.reset(new SensorWakeupWorker());
    //Lora发送广播唤醒，下发AP信息
    logInfo("Starting sensor wakeup via LoRa broadcast...");
    logInfo("FastSyncDataManager::onFastSyncDataStart, ssid:" << m_spWifiCommManager->getWifiSsid()
            << ", password:") << m_spWifiCommManager->getWifiPassword();

    m_spSensorWakeupWorker->setSensorId(QStringLiteral("FF:FF:FF:FF:FF:FF:FF:FF "));
    m_spSensorWakeupWorker->setSensorType(quint8(0xFF));
    // 设置ssid名称
    m_spSensorWakeupWorker->setSsidName(m_spWifiCommManager->getWifiSsid());
    // 设置密码
    m_spSensorWakeupWorker->setPassword(m_spWifiCommManager->getWifiPassword());
    m_spSensorWakeupWorker->startWakeup();
    logInfo("Sensor wakeup broadcast sent successfully");

    //启动同步任务状态检查定时器
    logInfo("Starting sync task status check timer (10s interval)...");
    m_syncTaskStatusCheckTimerId = startTimer(1000*10);
    logInfo("Sync task status check timer started successfully");

    logInfo("Fast sync data task started");
}

void FastSyncDataManager::onFastSyncDataCancel()
{
    logInfo("FastSyncDataManager::onFastSyncDataCancel");
    
    if(!hasRunningTask()) {
        logWarnning("FastSyncDataManager::onFastSyncDataCancel:No running task");
        return;
    }

    logInfo("Stopping fast sync data task...");    
    stopFastSyncData();
    logInfo("Fast sync data task stopped");    
    
    // 设置任务状态为取消
    m_currentTask->setTaskCanceled();
    
    emit sigFastSyncDataStatusUpdate();
    
    logInfo("Fast sync data task canceled successfully");    
}
